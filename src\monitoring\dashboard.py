# backend/src/monitoring/dashboard.py
import streamlit as st
from sqlalchemy.orm import sessionmaker
from models import engine, Trade, PerformanceMetric
import pandas as pd
import plotly.express as px
from datetime import datetime, timedelta, timezone
import time
import logging
import uuid

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("trading.dashboard")

# Create database session
Session = sessionmaker(bind=engine)

class TradingDashboard:
    def __init__(self):
        self.update_interval = 5  # seconds
        self.time_windows = {
            "24h": timedelta(hours=24),
            "7d": timedelta(days=7),
            "30d": timedelta(days=30)
        }
        self.session_id = str(uuid.uuid4())  # Unique session identifier

    def get_metrics(self, time_window: timedelta = None):
        """Get performance metrics with proper UTC handling"""
        with Session() as session:
            query = session.query(PerformanceMetric)
            
            if time_window:
                cutoff = datetime.now(timezone.utc) - time_window
                query = query.filter(PerformanceMetric.timestamp >= cutoff)
                
            return query.order_by(PerformanceMetric.timestamp.desc()).first()

    def get_trades(self, time_window: timedelta = None):
        """Get trades with proper UTC handling"""
        with Session() as session:
            query = session.query(Trade)
            
            if time_window:
                cutoff = datetime.now(timezone.utc) - time_window
                query = query.filter(Trade.timestamp >= cutoff)
                
            return pd.DataFrame([{
                'timestamp': t.timestamp.replace(tzinfo=None),
                'symbol': t.symbol,
                'side': t.side,
                'amount': t.amount,
                'price': t.price,
                'pnl': t.pnl,
                'strategy': t.strategy,
                'status': 'Open' if t.is_open else 'Closed'
            } for t in query.order_by(Trade.timestamp.desc()).limit(500).all()])

    def create_chart(self, trades_df: pd.DataFrame, chart_type: str):
        """Create charts with guaranteed unique keys"""
        if trades_df.empty:
            return None
            
        if chart_type == "equity":
            data = trades_df.sort_values('timestamp')['pnl'].cumsum()
            fig = px.line(
                data,
                title="Equity Curve",
                labels={'value': 'PnL', 'index': 'Trade Sequence'}
            )
            key = f"equity_{self.session_id}_{time.time()}"
        else:
            fig = px.pie(
                trades_df,
                names='strategy',
                title='Strategy Distribution'
            )
            key = f"strategy_{self.session_id}_{time.time()}"
        
        fig.update_layout(
            hovermode="x unified",
            showlegend=False,
            margin=dict(l=20, r=20, t=40, b=20)
        )
        return fig, key

    def display(self):
        """Main dashboard display with all fixes"""
        st.set_page_config(
            layout="wide",
            page_title="Live Trading Dashboard",
            page_icon="📊"
        )
        
        st.title("Live Trading Dashboard")
        
        # Unique key for time window selector
        time_window = st.sidebar.selectbox(
            "Time Window",
            list(self.time_windows.keys()),
            index=0,
            key=f"time_window_{self.session_id}"
        )
        
        placeholder = st.empty()
        
        while True:
            try:
                metrics = self.get_metrics(self.time_windows[time_window])
                trades_df = self.get_trades(self.time_windows[time_window])
                
                with placeholder.container():
                    # Clear previous content
                    st.empty()
                    
                    # Metrics
                    cols = st.columns(4)
                    cols[0].metric("Total PnL", f"${metrics.total_pnl:,.2f}" if metrics else "$0.00")
                    cols[1].metric("Sharpe Ratio", f"{metrics.sharpe_ratio:.2f}" if metrics else "0.00")
                    cols[2].metric("Win Rate", f"{metrics.win_rate:.1%}" if metrics else "0%")
                    cols[3].metric("Max Drawdown", f"{metrics.max_drawdown:.1%}" if metrics else "0%")
                    
                    # Charts with unique keys
                    if not trades_df.empty:
                        equity_fig, equity_key = self.create_chart(trades_df, "equity")
                        strategy_fig, strategy_key = self.create_chart(trades_df, "strategy")
                        
                        col1, col2 = st.columns([2, 1])
                        with col1:
                            st.plotly_chart(equity_fig, use_container_width=True, key=equity_key)
                        with col2:
                            st.plotly_chart(strategy_fig, use_container_width=True, key=strategy_key)
                    
                    # Trade table with unique key
                    st.dataframe(
                        trades_df,
                        column_config={
                            "timestamp": "Time",
                            "pnl": st.column_config.NumberColumn("PnL", format="$%.2f")
                        },
                        hide_index=True,
                        use_container_width=True,
                        key=f"trades_table_{self.session_id}_{time.time()}"
                    )
                    
                    st.caption(f"Last update: {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
                    
                time.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Dashboard error: {e}")
                time.sleep(10)
import requests

def get_hardware_status():
    try:
        response = requests.get("http://localhost:8000/status")
        return response.json()
    except:
        return {"error": "Hardware monitor unavailable"}

if __name__ == "__main__":
    dashboard = TradingDashboard()
    dashboard.display()
    hardware_status = get_hardware_status()
st.sidebar.metric("CPU Load", f"{hardware_status.get('cpu_load', 0)}%")
st.sidebar.metric("Memory", f"{hardware_status.get('memory_usage', 0)}%")
st.sidebar.metric("Disk Usage", f"{hardware_status.get('disk_usage', 0)}%")
deepspeed_status = requests.get("http://localhost:8000/deepspeed-status").json()
st.sidebar.json(deepspeed_status)