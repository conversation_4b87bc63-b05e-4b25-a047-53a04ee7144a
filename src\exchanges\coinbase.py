# src/exchanges/coinbase.py
import cbpro
from src.vault_client import VaultClient

class CoinbaseProTrader:
    def __init__(self):
        vault = VaultClient()
        credentials = vault.get_exchange_secret('coinbase')
        
        self.client = cbpro.AuthenticatedClient(
            credentials['api_key'],
            credentials['api_secret'],
            passphrase=vault.crypto.decrypt_value(os.getenv('COINBASE_PASSPHRASE')),
            api_url="https://api.pro.coinbase.com"
        )

    def get_balance(self) -> dict:
        """Get real-time portfolio balances"""
        try:
            accounts = self.client.get_accounts()
            return {
                acc['currency']: {
                    'balance': float(acc['balance']),
                    'available': float(acc['available']),
                    'hold': float(acc['hold'])
                }
                for acc in accounts if float(acc['balance']) > 0
            }
        except cbpro.exceptions.CoinbaseAPIError as e:
            raise SystemError(f"Coinbase API Error: {str(e)}")