# src/vault_client.py
import os
import hvac
from src.utils.cryptography.hybrid import HybridCrypto

class VaultClient:
    def __init__(self):
        self.client = hvac.Client(
            url=os.getenv('VAULT_URL'),
            token=os.getenv('VAULT_TOKEN'),
            cert=(
                'src/utils/cryptography/vault.crt', 
                'src/utils/cryptography/vault.key'
            ),
            verify='src/utils/cryptography/ca.crt'
        )
        self.crypto = HybridCrypto('src/utils/cryptography/private.pem')
        
        if not self.client.is_authenticated():
            raise ConnectionError("Vault authentication failed")

    def get_exchange_secret(self, exchange: str) -> dict:
        """Retrieve and decrypt exchange credentials"""
        secret_path = f"trading/exchanges/{exchange}"
        encrypted = self.client.secrets.kv.v2.read_secret_version(
            path=secret_path
        )['data']['data']
        
        return {
            'api_key': self.crypto.decrypt_value(encrypted['api_key']),
            'api_secret': self.crypto.decrypt_value(encrypted['api_secret'])
        }