# Python
__pycache__/
*.py[cod]
*.pyc
.python-version
*.egg-info/
*.egg
dist/
build/
.env
.venv/
venv/
/config/config.json
/config/secrets.*
/data/
/logs/

# Testing
.pytest_cache/

# Poetry
poetry.lock

# IDE
.vscode/
.idea/
*.swp

# System
.DS_Store
Thumbs.db

# Docker
docker-compose.db.yml
secure_storage/
/backend/.env
/backend/config/secrets.*
/config/config.json
/config/secrets.*

# .gitignore
.env.vault
*.hcl
vault.log

vault.zip
vault/vault.exe

echo "vault.crt" >> .gitignore
echo "vault.csr" >> .gitignore
echo "vault.key" >> .gitignore
.env
