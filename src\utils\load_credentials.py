# Create credential loader script (src/utils/load_credentials.py)
import hvac
import getpass

def main():
    print("🛡️ Enter Coinbase Pro Credentials Securely:")
    key = getpass.getpass("API Key: ")
    secret = getpass.getpass("API Secret: ")
    passphrase = getpass.getpass("Passphrase: ")
    
    vault = hvac.Client(url='http://localhost:8200', token=os.environ['VAULT_TOKEN'])
    vault.secrets.kv.v2.create_or_update_secret(
        path='coinbase/pro',
        secret={
            'api_key': key,
            'api_secret': secret,
            'passphrase': passphrase
        }
    )
    print("🔐 Credentials stored in Vault with version 2 encryption")

if __name__ == "__main__":
    main()