# src/utils/cryptography/secure_coinbase.py
from cryptography.hazmat.primitives import serialization
from src.utils.cryptography.hybrid import HybridCrypto

# Encrypt the EC key immediately
crypto = HybridCrypto('src/utils/cryptography/private.pem')
ec_key = os.getenv('COINBASE_EC_KEY').replace('\\n', '\n')  # Fix newlines

# Convert to secure format
pem_key = serialization.load_pem_private_key(
    ec_key.encode(),
    password=None
).private_bytes(
    encoding=serialization.Encoding.PEM,
    format=serialization.PrivateFormat.PKCS8,
    encryption_algorithm=serialization.NoEncryption()
)

encrypted_key = crypto.encrypt(pem_key)
with open('src/utils/cryptography/encrypted_coinbase.key', 'wb') as f:
    f.write(encrypted_key)