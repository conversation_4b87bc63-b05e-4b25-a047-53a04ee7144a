from ray.rllib.algorithms.ppo import PPOConfig
from ray.rllib.models import ModelCatalog
from src.neural.lstm_processor import LSTMTradingProcessor

class RLAgentManager:
    def __init__(self, config):
        ModelCatalog.register_custom_model("lstm_trading", LSTMTradingProcessor)
        
        self.agent = (PPOConfig()
                      .environment(env="TradingEnv")
                      .framework("torch")
                      .training(model={"custom_model": "lstm_trading"})
                      .build())
        
    async def get_action(self, processed_state):
        return self.agent.compute_single_action(processed_state)
    
    async def update_policy(self, batch):
        self.agent.train()
        return self.agent.update(batch)